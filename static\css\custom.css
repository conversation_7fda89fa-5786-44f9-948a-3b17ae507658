.bg-mission {
  background-color: var(--mission-color) !important;
}

.bg-dark {
  background-color: #6c757d !important;
}

body.light-theme .badge-dark {
  background-color: #6c757d;
  color: white;
}

.badge-mission {
  background-color: #fd7e14;
  color: white;
}

.badge-recipient {
  background-color: #0d6efd;
  color: white;
}

.badge-shooting {
  background-color: #C8BBBE;
  color: #333;
}

.bg-maintenance {
  background-color: var(--maintenance-color) !important;
  color: #333 !important;
}

.badge-maintenance {
  background-color: var(--maintenance-color);
  color: #333;
}

/* تحسينات صفحة كشف الاستلامات */
.receipts-table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 20px;
}

.receipts-table th,
.receipts-table td {
  border: 1px solid #dee2e6;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

.receipts-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  position: relative;
}

.receipts-table .editable-cell {
  background: transparent !important;
  border: none;
  width: 100%;
  padding: 4px;
  text-align: center;
  font-size: 14px;
  color: var(--text-primary) !important;
}

.receipts-table .editable-cell:focus {
  outline: 2px solid #007bff;
  background-color: transparent !important;
  color: var(--text-primary) !important;
}

.receipts-table .row-number {
  background-color: var(--bg-tertiary);
  color: var(--text-primary) !important;
  font-weight: bold;
  position: relative;
  min-width: 50px;
  border: 1px solid var(--border-color);
}

.receipts-table .row-number-cell {
  position: relative;
  min-width: 60px;
  color: var(--text-primary) !important;
  background-color: var(--bg-tertiary);
}

.receipts-table .row-delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  position: absolute;
  top: 2px;
  right: 2px;
  cursor: pointer;
  display: none;
}

.receipts-table .row-number:hover .row-delete-btn,
.receipts-table .row-number-cell:hover .row-delete-btn {
  display: block;
}

/* تحسينات أزرار التحكم في الصفوف - بدون خلفية */
.row-controls {
  position: absolute;
  top: 50%;
  right: 2px;
  transform: translateY(-50%);
  display: none;
  flex-direction: column;
  gap: 2px;
  background: transparent !important;
  backdrop-filter: none !important;
  padding: 0 !important;
}

.receipts-table .row-number-cell:hover .row-controls {
  display: flex;
}

.column-controls {
  display: none;
  position: absolute;
  top: 2px;
  right: 2px;
  flex-direction: row;
  gap: 2px;
  z-index: 10;
}

.receipts-table th:hover .column-controls,
#patrolTable.receipts-table th:hover .column-controls {
  display: flex;
}

/* تم نقل تنسيق أزرار الأعمدة إلى الأسفل لتوحيد التصميم */

/* تحسينات أزرار التحكم العامة - شفافة بدون خلفية */
.control-btn {
  width: 20px !important;
  height: 20px !important;
  border-radius: 50% !important;
  border: 2px solid !important;
  font-size: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  margin: 1px !important;
  background: transparent !important;
  font-weight: bold !important;
}

.control-btn:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
}

.control-btn.btn-add {
  color: #28a745 !important;
  border-color: #28a745 !important;
  background: transparent !important;
}

.control-btn.btn-add:hover {
  background: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.4) !important;
}

.control-btn.btn-delete {
  color: #dc3545 !important;
  border-color: #dc3545 !important;
  background: transparent !important;
}

.control-btn.btn-delete:hover {
  background: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.4) !important;
}

.editable-header {
  background: transparent;
  border: none;
  width: 100%;
  text-align: center;
  font-weight: bold;
  padding: 4px;
}

.editable-header:focus {
  outline: 2px solid #007bff;
  background-color: #fff3cd;
}

.location-select {
  width: 100%;
  border: 1px solid var(--border-color) !important;
  background: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  padding: 4px;
  text-align: center;
}

.location-select:focus {
  outline: 2px solid #007bff;
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-color) !important;
}

.location-select option {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  padding: 5px;
}

/* تحسينات عامة للقوائم المنسدلة في كشف الاستلامات */
.receipts-table select {
  background: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

.receipts-table select option {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* مؤشر حالة الحفظ */
#saveStatus {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تحسينات حقل البحث */
#searchNationalId {
  color: #000 !important;
  background-color: #fff !important;
  border: 2px solid #007bff !important;
  font-size: 16px !important;
  padding: 10px !important;
  direction: ltr;
  text-align: left;
}

#searchNationalId:focus {
  outline: none !important;
  border-color: #0056b3 !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  background-color: #fff !important;
  color: #000 !important;
}

#searchNationalId::placeholder {
  color: #6c757d !important;
  opacity: 1;
}

/* تحسينات إضافية للخلايا */
input.editable-cell {
  background: transparent !important;
  color: var(--text-primary) !important;
  border: none !important;
}

input.editable-cell:focus {
  background: transparent !important;
  color: var(--text-primary) !important;
  outline: 1px solid #007bff !important;
}

/* تحسينات عامة للحقول */
.form-control {
  background-color: transparent !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  background-color: transparent !important;
  color: var(--text-primary) !important;
  border-color: #007bff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* تحسينات إضافية للنص المتكيف */
.receipts-table input[type="text"] {
  color: var(--text-primary) !important;
  background: transparent !important;
}

.receipts-table td input {
  color: var(--text-primary) !important;
  background: transparent !important;
}

.receipts-table .editable-cell,
.receipts-table input.editable-cell {
  color: var(--text-primary) !important;
  background: transparent !important;
}

/* تصحيح ألوان الأرقام في الجدول - لون داكن منسق */
.receipts-table tbody tr td:first-child {
  color: white !important;
  background-color: #3a3a3a !important;
}

.receipts-table tbody tr td:first-child span {
  color: white !important;
  background-color: transparent !important;
}

.receipts-table .row-number {
  color: white !important;
  background-color: #3a3a3a !important;
}

/* إزالة أي لون أزرق من الأرقام */
.receipts-table .row-number-cell,
.receipts-table .row-number-cell *,
.receipts-table tbody tr td:first-child,
.receipts-table tbody tr td:first-child * {
  color: white !important;
  background-color: #3a3a3a !important;
}

/* تصحيح ألوان العناوين والأرقام */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
}

.receipts-table .row-number-cell span {
  color: var(--text-primary) !important;
}

.receipts-table td:first-child {
  color: var(--text-primary) !important;
}

/* تحسينات للطباعة */
@media print {
  .receipts-table .row-delete-btn,
  .column-controls,
  .btn-column-control {
    display: none !important;
  }

  .receipts-table th,
  .receipts-table td {
    border: 1px solid #000 !important;
    padding: 8px !important;
  }

  .receipts-table input {
    color: #000 !important;
    background: #fff !important;
  }

  .receipts-table .row-number,
  .receipts-table .row-number-cell,
  .receipts-table .row-number-cell span {
    color: #000 !important;
    background: #f8f9fa !important;
  }

  .receipts-table th:first-child {
    color: #000 !important;
    background: #e9ecef !important;
  }

  h1, h2, h3, h4, h5, h6 {
    color: #000 !important;
  }

  #saveStatus {
    display: none !important;
  }
}

/* تأكيد تطبيق نفس التنسيق على جدول ملاحظات الدوريات */
#patrolTable.receipts-table .row-number-cell,
#patrolTable.receipts-table .row-number-cell *,
#patrolTable.receipts-table tbody tr td:first-child,
#patrolTable.receipts-table tbody tr td:first-child * {
  color: white !important;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

#patrolTable.receipts-table .row-number {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  font-weight: bold;
  position: relative;
  width: 60px;
  max-width: 60px;
  border: 1px solid var(--border-color);
}

#patrolTable.receipts-table .row-number-cell {
  position: relative;
  width: 60px;
  max-width: 60px;
  color: white !important;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

#patrolTable.receipts-table .row-number-cell:hover .row-controls {
  display: flex;
}

/* ضمان تطابق تنسيق الأرقام في كلا الجدولين */
.receipts-table .row-number,
#patrolTable.receipts-table .row-number {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  font-weight: bold;
  border: 1px solid var(--border-color);
  text-align: center;
  vertical-align: middle;
}

/* ضمان تطابق تنسيق خلايا الأرقام في كلا الجدولين - محسن */
.receipts-table .row-number-cell {
  background-color: #3a3a3a !important;
  color: white !important;
  position: relative !important;
  width: 60px !important;
  max-width: 60px !important;
  text-align: center !important;
  vertical-align: middle !important;
  border: 1px solid var(--border-color) !important;
  padding: 8px 25px 8px 8px !important;
  overflow: visible !important;
}

/* تنسيق خاص لجدول الدوريات فقط */
#patrolTable.receipts-table .row-number-cell {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  position: relative !important;
  width: 60px !important;
  max-width: 60px !important;
  text-align: center !important;
  vertical-align: middle !important;
  border: 1px solid var(--border-color) !important;
  padding: 8px 25px 8px 8px !important;
  overflow: visible !important;
}

/* ضمان تطابق تنسيق span الأرقام في كلا الجدولين - محسن */
.receipts-table .row-number-cell span,
#patrolTable.receipts-table .row-number-cell span,
.receipts-table .row-number,
#patrolTable.receipts-table .row-number {
  background: transparent !important;
  color: white !important;
  font-weight: bold !important;
  display: block !important;
  width: 100% !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
}

/* إزالة أي تنسيق مختلف من العمود الأول */
#patrolTable tbody tr td:first-child,
#patrolTable tbody tr td:first-child *,
.receipts-table tbody tr td:first-child,
.receipts-table tbody tr td:first-child * {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
}

/* ضمان تطابق تنسيق العناوين في كلا الجدولين */
.receipts-table th,
#patrolTable.receipts-table th {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  font-weight: bold;
  position: relative;
  text-align: center;
  vertical-align: middle;
  border: 1px solid var(--border-color) !important;
}

/* تحسين عنوان عمود الرقم - لون داكن منسق */
.receipts-table th:first-child,
#patrolTable.receipts-table th:first-child {
  background-color: #2c2c2c !important;
  color: white !important;
  font-weight: bold !important;
  width: 60px !important;
  max-width: 60px !important;
  text-align: center !important;
  font-size: 14px !important;
  border: 1px solid var(--border-color) !important;
  padding: 12px 8px !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
}

/* تحسين عنوان عمود الرقم في الوضع المظلم */
[data-theme="dark"] .receipts-table th:first-child,
[data-theme="dark"] #patrolTable.receipts-table th:first-child {
  background-color: #2c2c2c !important;
  color: white !important;
  border-color: var(--border-color) !important;
}

/* ضمان تطابق تنسيق الخلايا القابلة للتحرير في كلا الجدولين */
.receipts-table .editable-cell,
#patrolTable.receipts-table .editable-cell {
  background: transparent !important;
  border: none;
  width: 100%;
  padding: 4px;
  text-align: center;
  font-size: 14px;
  color: var(--text-primary) !important;
}

.receipts-table .editable-cell:focus,
#patrolTable.receipts-table .editable-cell:focus {
  outline: 2px solid #007bff;
  background-color: transparent !important;
  color: var(--text-primary) !important;
}

/* تم دمج هذا التعريف مع التعريف الشامل أعلاه */

/* تم دمج تنسيق أزرار التحكم في التعريف العام أعلاه */

/* ضمان تطابق حاويات أزرار التحكم - محسن */
.receipts-table .row-controls,
#patrolTable.receipts-table .row-controls,
#receiptTable .row-controls,
table.receipts-table .row-controls {
  position: absolute !important;
  top: 50% !important;
  right: 2px !important;
  transform: translateY(-50%) !important;
  display: none !important;
  flex-direction: column !important;
  gap: 2px !important;
  z-index: 10 !important;
  background: transparent !important;
  backdrop-filter: none !important;
  padding: 0 !important;
}

.receipts-table .row-number-cell:hover .row-controls,
#patrolTable.receipts-table .row-number-cell:hover .row-controls,
#receiptTable .row-number-cell:hover .row-controls,
table.receipts-table .row-number-cell:hover .row-controls {
  display: flex !important;
}

/* ضمان أن الأزرار تظهر بشكل صحيح */
.receipts-table .row-number-cell:hover,
#patrolTable.receipts-table .row-number-cell:hover {
  overflow: visible !important;
  z-index: 20 !important;
}

.receipts-table .row-number-cell,
#patrolTable.receipts-table .row-number-cell {
  overflow: visible !important;
  position: relative !important;
}

/* تم نقل تنسيق الأزرار إلى التعريف العام أعلاه */

/* توحيد تصميم جميع أزرار الإضافة */
.btn-success,
.btn.btn-success {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
  font-weight: 500 !important;
}

.btn-success:hover,
.btn.btn-success:hover {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.btn-success:focus,
.btn.btn-success:focus {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
  color: white !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.btn-success:active,
.btn.btn-success:active {
  background-color: #1e7e34 !important;
  border-color: #1c7430 !important;
  color: white !important;
}

/* ضمان أن أيقونة + تظهر بوضوح */
.btn-success i.fas.fa-plus,
.btn.btn-success i.fas.fa-plus {
  color: white !important;
  font-weight: bold !important;
  margin-right: 5px !important;
}

/* CSS عالي الأولوية للأزرار الرئيسية في صفحة كشف الاستلامات */
.table-controls .btn.btn-success,
.table-controls button.btn-success,
button[onclick="addColumn()"],
button[onclick="addRow()"],
button[onclick="addPatrolColumn()"],
button[onclick="addPatrolRow()"],
button[onclick="addNewRow()"] {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
  font-weight: 500 !important;
}

.table-controls .btn.btn-success:hover,
.table-controls button.btn-success:hover,
button[onclick="addColumn()"]:hover,
button[onclick="addRow()"]:hover,
button[onclick="addPatrolColumn()"]:hover,
button[onclick="addPatrolRow()"]:hover,
button[onclick="addNewRow()"]:hover {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

/* إزالة أي تنسيق أصفر متبقي */
.btn-warning.btn-success,
button.btn-warning[onclick*="add"] {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
}

/* CSS شامل لجميع أزرار الإضافة في الصفحة */
#receiptTable ~ .table-controls .btn,
#patrolTable ~ .table-controls .btn,
.card-body .table-controls .btn,
.receipts-container .btn {
  transition: all 0.3s ease !important;
}

/* تأكيد أن جميع أزرار الإضافة خضراء */
.btn[onclick*="add"]:not([onclick*="delete"]),
.btn[onclick*="Add"]:not([onclick*="delete"]),
.btn[onclick*="إضافة"] {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
}

.btn[onclick*="add"]:not([onclick*="delete"]):hover,
.btn[onclick*="Add"]:not([onclick*="delete"]):hover,
.btn[onclick*="إضافة"]:hover {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

/* ضمان تطابق أزرار الأعمدة مع أزرار الصفوف */
.column-controls .control-btn,
.receipts-table .column-controls .control-btn,
#patrolTable.receipts-table .column-controls .control-btn {
  width: 20px !important;
  height: 20px !important;
  border-radius: 3px !important;
  border: none !important;
  font-size: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  margin: 1px !important;
}

.column-controls .control-btn.btn-add,
.receipts-table .column-controls .control-btn.btn-add,
#patrolTable.receipts-table .column-controls .control-btn.btn-add {
  background: #28a745 !important;
  color: white !important;
  border: 1px solid #28a745 !important;
}

.column-controls .control-btn.btn-add:hover,
.receipts-table .column-controls .control-btn.btn-add:hover,
#patrolTable.receipts-table .column-controls .control-btn.btn-add:hover {
  background: #218838 !important;
  border-color: #1e7e34 !important;
  color: white !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.column-controls .control-btn.btn-delete,
.receipts-table .column-controls .control-btn.btn-delete,
#patrolTable.receipts-table .column-controls .control-btn.btn-delete {
  background: #dc3545 !important;
  color: white !important;
  border: 1px solid #dc3545 !important;
}

.column-controls .control-btn.btn-delete:hover,
.receipts-table .column-controls .control-btn.btn-delete:hover,
#patrolTable.receipts-table .column-controls .control-btn.btn-delete:hover {
  background: #c82333 !important;
  border-color: #bd2130 !important;
  color: white !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

/* ضمان ظهور الأيقونات بوضوح */
.control-btn i.fas.fa-plus,
.control-btn i.fas.fa-times {
  color: white !important;
  font-weight: bold !important;
}

/* توحيد تصميم أزرار إضافة الأعمدة في رؤوس الجداول */
.btn-column-control,
.btn-column-control.btn-add-column {
  background: #28a745 !important;
  color: white !important;
  border: 1px solid #28a745 !important;
  border-radius: 3px !important;
  width: 20px !important;
  height: 20px !important;
  font-size: 12px !important;
  font-weight: bold !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  margin: 1px !important;
}

.btn-column-control:hover,
.btn-column-control.btn-add-column:hover {
  background: #218838 !important;
  border-color: #1e7e34 !important;
  color: white !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

/* أزرار حذف الأعمدة */
.btn-column-control:not(.btn-add-column) {
  background: #dc3545 !important;
  border-color: #dc3545 !important;
}

.btn-column-control:not(.btn-add-column):hover {
  background: #c82333 !important;
  border-color: #bd2130 !important;
}

/* ضمان ظهور الأيقونات بوضوح */
.btn-column-control i,
.btn-column-control.btn-add-column i {
  color: white !important;
  font-weight: bold !important;
}

/* ضمان تطابق تنسيق القوائم المنسدلة في كلا الجدولين */
.receipts-table .location-select,
.receipts-table .patrol-location-select,
#patrolTable.receipts-table .location-select,
#patrolTable.receipts-table .patrol-location-select {
  width: 100% !important;
  padding: 4px !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 4px !important;
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-size: 14px !important;
  text-align: center !important;
  appearance: auto !important;
  -webkit-appearance: auto !important;
  -moz-appearance: auto !important;
}

.receipts-table .location-select:focus,
.receipts-table .patrol-location-select:focus,
#patrolTable.receipts-table .location-select:focus,
#patrolTable.receipts-table .patrol-location-select:focus {
  outline: 2px solid #007bff !important;
  border-color: #007bff !important;
}

/* إزالة أي تنسيق مختلف من Bootstrap أو CSS آخر */
#patrolTable select,
#patrolTable .form-select,
#patrolTable .form-control {
  width: 100% !important;
  padding: 4px !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 4px !important;
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-size: 14px !important;
  text-align: center !important;
}

/* ضمان تطابق تنسيق الحدود والمساحات في كلا الجدولين */
.receipts-table th,
.receipts-table td,
#patrolTable.receipts-table th,
#patrolTable.receipts-table td {
  border: 1px solid #dee2e6;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
  border-collapse: collapse;
}

/* إزالة المساحات الزائدة من الجدول */
.receipts-table,
#patrolTable.receipts-table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  margin: 0 !important;
  width: 100% !important;
}

/* تحسين تنسيق عمود الأرقام */
.receipts-table tbody tr td:first-child,
#patrolTable.receipts-table tbody tr td:first-child {
  padding: 8px 4px !important;
  margin: 0 !important;
  border-right: 1px solid #dee2e6 !important;
}

/* إزالة المساحات الزائدة من حاوي الجدول */
.table-responsive {
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.table-responsive .table {
  margin-bottom: 0 !important;
}

/* ضمان عدم وجود مساحات زائدة في الخلايا */
.receipts-table td,
.receipts-table th,
#patrolTable.receipts-table td,
#patrolTable.receipts-table th {
  box-sizing: border-box !important;
  margin: 0 !important;
}

/* تحسينات الطباعة لكلا الجدولين */
@media print {
  .receipts-table .row-delete-btn,
  .receipts-table .row-controls,
  .receipts-table .control-btn,
  #patrolTable.receipts-table .row-delete-btn,
  #patrolTable.receipts-table .row-controls,
  #patrolTable.receipts-table .control-btn,
  .column-controls,
  .btn-column-control {
    display: none !important;
  }

  .receipts-table th,
  .receipts-table td,
  #patrolTable.receipts-table th,
  #patrolTable.receipts-table td {
    border: 1px solid #000 !important;
    padding: 8px !important;
  }

  .receipts-table input,
  .receipts-table select,
  #patrolTable.receipts-table input,
  #patrolTable.receipts-table select {
    color: #000 !important;
    background: #fff !important;
    border: none !important;
  }

  .receipts-table .row-number,
  .receipts-table .row-number-cell,
  .receipts-table .row-number-cell span,
  #patrolTable.receipts-table .row-number,
  #patrolTable.receipts-table .row-number-cell,
  #patrolTable.receipts-table .row-number-cell span {
    color: #000 !important;
    background: #fff !important;
  }
}

/* إزالة أي تنسيق مختلف من Bootstrap أو CSS آخر */
#patrolTable td,
#patrolTable th,
#patrolTable tr {
  background-color: inherit !important;
  color: inherit !important;
}

/* ضمان أن جدول ملاحظات الدوريات يحصل على نفس تنسيق الجدول الرئيسي */
#patrolTable.receipts-table {
  width: 100% !important;
  margin-bottom: 1rem !important;
  border-collapse: collapse !important;
}

#patrolTable.receipts-table td,
#patrolTable.receipts-table th {
  border: 1px solid #dee2e6 !important;
  padding: 8px !important;
  text-align: center !important;
  vertical-align: middle !important;
}

#patrolTable.receipts-table th {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  font-weight: bold !important;
  position: relative !important;
  border: 1px solid var(--border-color) !important;
}

/* تأكيد تطابق الألوان في الوضع المظلم والفاتح */
body:not(.light-theme) #patrolTable.receipts-table .row-number-cell,
body:not(.light-theme) #patrolTable.receipts-table .row-number-cell *,
body:not(.light-theme) .receipts-table .row-number-cell,
body:not(.light-theme) .receipts-table .row-number-cell * {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

body.light-theme #patrolTable.receipts-table .row-number-cell,
body.light-theme #patrolTable.receipts-table .row-number-cell *,
body.light-theme .receipts-table .row-number-cell,
body.light-theme .receipts-table .row-number-cell * {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

/* CSS عالي الأولوية لضمان التطابق الكامل */
table#patrolTable.receipts-table tbody tr td:first-child {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: #ffffff !important;
  font-weight: bold !important;
  text-align: center !important;
  vertical-align: middle !important;
  position: relative !important;
  min-width: 60px !important;
  border: 1px solid var(--border-color) !important;
  padding: 8px !important;
}

table#patrolTable.receipts-table tbody tr td:first-child span {
  background: transparent !important;
  color: #ffffff !important;
  font-weight: bold !important;
  display: inline-block !important;
  width: 100% !important;
  text-align: center !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

table#patrolTable.receipts-table select.patrol-location-select,
table#patrolTable.receipts-table select.location-select {
  width: 100% !important;
  padding: 4px !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 4px !important;
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-size: 14px !important;
  text-align: center !important;
  appearance: auto !important;
  -webkit-appearance: auto !important;
  -moz-appearance: auto !important;
}

/* تحسينات شاملة للقوائم المنسدلة في جميع أنحاء الموقع */
select,
.form-select,
.form-control[type="select"] {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
}

select:focus,
.form-select:focus,
.form-control[type="select"]:focus {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--accent-color) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

select option,
.form-select option,
.form-control[type="select"] option {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  padding: 8px;
}

/* تحسينات للقوائم المنسدلة في Bootstrap */
.dropdown-menu {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

.dropdown-item.active,
.dropdown-item:active {
  background-color: var(--accent-color) !important;
  color: white !important;
}
