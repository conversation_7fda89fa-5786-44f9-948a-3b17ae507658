/* تنسيق جداول الكشوفات */

/* تنسيق خلايا أرقام الصفوف المحسن - متناسق مع العنوان */
.row-number-cell {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white !important;
    font-weight: bold !important;
    width: 60px !important;
    max-width: 60px !important;
    position: relative !important;
    text-align: center !important;
    vertical-align: middle !important;
    border: 1px solid var(--border-color) !important;
    padding: 8px 25px 8px 8px !important;
    overflow: visible !important;
}

/* تنسيق رقم الصف المحسن - متناسق مع الخلفية الداكنة */
.row-number {
    color: #ffffff !important;
    font-weight: bold !important;
    font-size: 16px !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
    background: transparent !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* تنسيق أزرار التحكم في الصفوف - بدون خلفية لتجنب تغطية الأرقام */
.row-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 2px !important;
    position: absolute !important;
    right: 2px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
    background: transparent !important;
    backdrop-filter: none !important;
    padding: 0 !important;
}

/* إظهار أزرار التحكم عند التمرير - بدون خلفية */
.row-number-cell:hover .row-controls,
td:hover .row-controls {
    opacity: 1 !important;
    transform: translateY(-50%) !important;
    background: transparent !important;
}

/* تنسيق أزرار التحكم في الأعمدة */
.column-controls {
    display: flex !important;
    gap: 3px !important;
    position: absolute !important;
    top: 3px !important;
    left: 3px !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
}

/* إظهار أزرار التحكم في الأعمدة عند التمرير */
th:hover .column-controls {
    opacity: 1 !important;
    transform: scale(1.05) !important;
}

/* تنسيق أزرار التحكم المحسن - شفافة بدون خلفية */
.control-btn {
    background: transparent !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 10px !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.control-btn:hover::before {
    transform: translateX(100%);
}

/* تنسيق زر الإضافة المحسن - شفاف مع حدود ملونة */
.control-btn.btn-add {
    color: #28a745 !important;
    border-color: #28a745 !important;
    background: transparent !important;
    font-weight: bold !important;
}

.control-btn.btn-add:hover {
    background: #28a745 !important;
    color: white !important;
    border-color: #28a745 !important;
    transform: scale(1.1) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.5) !important;
}

.control-btn.btn-add:active {
    transform: scale(1.05) !important;
    box-shadow: 0 1px 4px rgba(40, 167, 69, 0.4) !important;
}

/* تنسيق زر الحذف المحسن - شفاف مع حدود ملونة */
.control-btn.btn-delete {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
    background: transparent !important;
    font-weight: bold !important;
}

.control-btn.btn-delete:hover {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
    transform: scale(1.1) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.5) !important;
}

.control-btn.btn-delete:active {
    transform: scale(1.05) !important;
    box-shadow: 0 1px 4px rgba(220, 53, 69, 0.4) !important;
}

/* تنسيق الحقول القابلة للتحرير */
.editable-cell {
    width: 100% !important;
    border: none !important;
    background: transparent !important;
    text-align: center !important;
    padding: 8px !important;
    font-size: 14px !important;
    color: var(--text-primary) !important;
}

.editable-cell:focus {
    outline: 2px solid #007bff !important;
    outline-offset: -2px !important;
    background: rgba(0, 123, 255, 0.1) !important;
}

/* تنسيق العناوين القابلة للتحرير */
.editable-header {
    width: 100% !important;
    border: none !important;
    background: transparent !important;
    text-align: center !important;
    font-weight: bold !important;
    color: var(--text-primary) !important;
    padding: 8px !important;
}

.editable-header:focus {
    outline: 2px solid #007bff !important;
    outline-offset: -2px !important;
    background: rgba(0, 123, 255, 0.1) !important;
}

/* تنسيق القوائم المنسدلة للمواقع */
.location-select {
    width: 100% !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    padding: 6px !important;
    background: white !important;
    color: var(--text-primary) !important;
    text-align: center !important;
}

.location-select:focus {
    outline: 2px solid #007bff !important;
    border-color: #007bff !important;
}

/* تنسيق الجداول */
.receipts-table {
    border-collapse: collapse !important;
    width: 100% !important;
}

.receipts-table th,
.receipts-table td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: center !important;
    vertical-align: middle !important;
    position: relative !important;
}

.receipts-table th {
    background-color: #f8f9fa !important;
    font-weight: bold !important;
    color: #333 !important;
}

/* تنسيق عنوان عمود الرقم المحسن - لون داكن منسق */
.receipts-table th:first-child {
    background-color: #2c2c2c !important;
    color: white !important;
    font-weight: bold !important;
    width: 60px !important;
    max-width: 60px !important;
    text-align: center !important;
    font-size: 14px !important;
    border: 1px solid var(--border-color) !important;
    padding: 12px 8px !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
}

/* تأكيد لون النص في الوضع النهاري */
[data-theme="light"] .receipts-table th:first-child,
.receipts-table th:first-child {
    background-color: #2c2c2c !important;
    color: white !important;
    border-color: var(--border-color) !important;
}



/* إخفاء أي عنصر يحتوي على نص الباركود */
*[class*="alert"] {
    &:has-text("لتسجيل المستلم") {
        display: none !important;
    }
    &:has-text("باركود") {
        display: none !important;
    }
}

/* إخفاء searchResults إذا كان يحتوي على إشعار الباركود */
#searchResults .alert.alert-info:contains("لتسجيل المستلم") {
    display: none !important;
}

/* تنسيق responsive للجداول */
.table-responsive {
    overflow-x: auto !important;
}

/* تنسيق أزرار التحكم في الجدول المحسنة */
.table-controls {
    margin-bottom: 20px !important;
    padding: 15px !important;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8), rgba(233, 236, 239, 0.6)) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.table-controls .btn {
    margin-right: 8px !important;
    margin-bottom: 8px !important;
    padding: 8px 16px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 2px solid transparent !important;
}

/* تأثير الموجة للأزرار */
.table-controls .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.table-controls .btn:active::before {
    width: 300px;
    height: 300px;
}

/* تنسيق أزرار النجاح */
.table-controls .btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border-color: #28a745 !important;
    color: white !important;
    box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3) !important;
}

.table-controls .btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4) !important;
    border-color: #1e7e34 !important;
}

/* تنسيق الأزرار الثانوية */
.table-controls .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    border-color: #6c757d !important;
    color: white !important;
    box-shadow: 0 3px 6px rgba(108, 117, 125, 0.3) !important;
}

.table-controls .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #495057) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(108, 117, 125, 0.4) !important;
    border-color: #495057 !important;
}

/* تنسيق الأزرار الأساسية */
.table-controls .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border-color: #007bff !important;
    color: white !important;
    box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3) !important;
}

.table-controls .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4) !important;
    border-color: #004085 !important;
}

/* تنسيق الكروت */
.card {
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #ddd !important;
    padding: 15px !important;
}

.card-body {
    padding: 20px !important;
}



[data-theme="dark"] .control-btn {
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.6) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] .control-btn.btn-add {
    color: #20c997 !important;
    border-color: #20c997 !important;
    background: transparent !important;
}

[data-theme="dark"] .control-btn.btn-delete {
    color: #e74c3c !important;
    border-color: #e74c3c !important;
    background: transparent !important;
}

[data-theme="dark"] .table-controls {
    background: linear-gradient(135deg, rgba(33, 37, 41, 0.8), rgba(52, 58, 64, 0.6)) !important;
    border-color: #555 !important;
}

[data-theme="dark"] .location-select {
    background: var(--bg-secondary) !important;
    border-color: #555 !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .receipts-table th,
[data-theme="dark"] .receipts-table td {
    border-color: #555 !important;
}

[data-theme="dark"] .card {
    border-color: #555 !important;
    background: var(--bg-secondary) !important;
}

[data-theme="dark"] .card-header {
    background-color: var(--bg-primary) !important;
    border-color: #555 !important;
}

/* تحسينات الأزرار الرئيسية */
.btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 10px 20px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 2px solid transparent !important;
    text-decoration: none !important;
}

/* تأثير الإضاءة للأزرار */
.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::after {
    left: 100%;
}

/* أزرار النجاح المحسنة */
.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border-color: #28a745 !important;
    color: white !important;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 16px rgba(40, 167, 69, 0.4) !important;
    border-color: #1e7e34 !important;
}

.btn-success:active {
    transform: translateY(0) !important;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
}

/* أزرار أساسية محسنة */
.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border-color: #007bff !important;
    color: white !important;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 16px rgba(0, 123, 255, 0.4) !important;
    border-color: #004085 !important;
}

.btn-primary:active {
    transform: translateY(0) !important;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

/* أزرار ثانوية محسنة */
.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    border-color: #6c757d !important;
    color: white !important;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3) !important;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #495057) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 16px rgba(108, 117, 125, 0.4) !important;
    border-color: #495057 !important;
}

.btn-secondary:active {
    transform: translateY(0) !important;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3) !important;
}

/* أزرار الخطر محسنة */
.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border-color: #dc3545 !important;
    color: white !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 16px rgba(220, 53, 69, 0.4) !important;
    border-color: #bd2130 !important;
}

.btn-danger:active {
    transform: translateY(0) !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
}

/* أزرار صغيرة محسنة */
.btn-sm {
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
}

/* تحسينات للأيقونات */
.btn i {
    margin-right: 6px !important;
    font-size: 14px !important;
    transition: transform 0.3s ease !important;
}

.btn:hover i {
    transform: scale(1.1) !important;
}

/* تأثيرات خاصة للأزرار المهمة */
.btn-success i.fa-save {
    color: #fff !important;
}

.btn-primary i.fa-print {
    color: #fff !important;
}

.btn-success i.fa-file-excel {
    color: #fff !important;
}

/* تحسينات إضافية للأزرار */
.btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25) !important;
}

.btn-success:focus {
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25) !important;
}

.btn-danger:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25) !important;
}

.btn-secondary:focus {
    box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.25) !important;
}

/* تأثيرات النقر */
.btn:active {
    transform: scale(0.98) !important;
}

/* تحسينات للأزرار الصغيرة في الجداول */
.control-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5) !important;
}

.control-btn.btn-add:focus {
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.5) !important;
}

.control-btn.btn-delete:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.5) !important;
}

/* تحسينات للأزرار عند التعطيل */
.btn:disabled,
.btn.disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.control-btn:disabled,
.control-btn.disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* تحسينات للأزرار في الوضع المظلم */
[data-theme="dark"] .btn {
    border-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .btn-success {
    background: linear-gradient(135deg, #198754, #157347) !important;
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.4) !important;
}

[data-theme="dark"] .btn-primary {
    background: linear-gradient(135deg, #0d6efd, #0b5ed7) !important;
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.4) !important;
}

[data-theme="dark"] .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5c636a) !important;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4) !important;
}

[data-theme="dark"] .btn-danger {
    background: linear-gradient(135deg, #dc3545, #bb2d3b) !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4) !important;
}

/* تأثيرات خاصة للأزرار المهمة */
.btn-success {
    position: relative !important;
}

.btn-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.btn-success:hover::before {
    transform: translateX(100%);
}

/* تحسينات للأيقونات في الأزرار */
.btn i.fas {
    transition: all 0.3s ease !important;
}

.btn:hover i.fas {
    transform: scale(1.1) rotate(5deg) !important;
}

.control-btn i.fas {
    transition: all 0.2s ease !important;
}

.control-btn:hover i.fas {
    transform: scale(1.2) !important;
}

/* تأثيرات الضوء للأزرار */
.btn {
    background-size: 200% 200% !important;
    animation: subtle-glow 3s ease-in-out infinite alternate !important;
}

@keyframes subtle-glow {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 100% 50%;
    }
}

/* إيقاف الأنيميشن عند التمرير */
.btn:hover {
    animation: none !important;
}

/* تحسينات خاصة لأزرار التحكم في الجداول */
.receipts-table .control-btn {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

.receipts-table .row-number-cell {
    position: relative !important;
    overflow: visible !important;
}

.receipts-table .row-controls {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(5px) !important;
    border-radius: 8px !important;
    padding: 2px !important;
}

.receipts-table th {
    position: relative !important;
    overflow: visible !important;
}

.receipts-table .column-controls {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(5px) !important;
    border-radius: 8px !important;
    padding: 2px !important;
}

/* تحسينات للأزرار عند الضغط */
.control-btn:active {
    transform: scale(0.9) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* تأثيرات خاصة للأزرار المختلفة */
.control-btn.btn-add {
    background-image: radial-gradient(circle at center, rgba(40, 167, 69, 0.1), transparent) !important;
}

.control-btn.btn-delete {
    background-image: radial-gradient(circle at center, rgba(220, 53, 69, 0.1), transparent) !important;
}

/* تحسينات للأزرار في الوضع المظلم */
[data-theme="dark"] .receipts-table .row-controls {
    background: rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .receipts-table .column-controls {
    background: rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .control-btn {
    background: linear-gradient(135deg, rgba(52, 58, 64, 0.9), rgba(33, 37, 41, 0.9)) !important;
    color: #fff !important;
}

[data-theme="dark"] .control-btn.btn-add {
    color: #20c997 !important;
    background-image: radial-gradient(circle at center, rgba(32, 201, 151, 0.2), transparent) !important;
}

[data-theme="dark"] .control-btn.btn-delete {
    color: #e74c3c !important;
    background-image: radial-gradient(circle at center, rgba(231, 76, 60, 0.2), transparent) !important;
}

/* تأثيرات الإضاءة للأزرار */
.control-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
}

.control-btn:hover::after {
    opacity: 1;
}

/* تحسينات للأزرار الكبيرة */
.table-controls .btn {
    min-width: 120px !important;
    justify-content: center !important;
    display: inline-flex !important;
    align-items: center !important;
}

.table-controls .btn i {
    margin-right: 8px !important;
}

/* تأثيرات خاصة للأزرار المهمة */
.btn-success.btn-lg,
.btn-primary.btn-lg {
    background-size: 400% 400% !important;
    animation: gradient-shift 4s ease infinite !important;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* تحسينات للأزرار عند التركيز */
.btn:focus-visible {
    outline: 2px solid currentColor !important;
    outline-offset: 2px !important;
}

.control-btn:focus-visible {
    outline: 1px solid currentColor !important;
    outline-offset: 1px !important;
}

/* توحيد جميع أزرار التحكم في الجداول */
.column-controls .control-btn,
.row-controls .control-btn {
    margin: 1px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* التأكد من أن جميع الأيقونات متسقة */
.control-btn i.fas {
    font-size: 10px !important;
    line-height: 1 !important;
}

/* إزالة أي تنسيقات قديمة للأزرار */
.btn-column-control,
.btn-add-column {
    display: none !important;
}

/* التأكد من أن الأزرار تظهر بنفس الحجم في جميع الجداول */
.receipts-table .control-btn {
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    padding: 0 !important;
    margin: 1px !important;
}

/* أزرار الصفوف أصغر لتجنب التداخل مع الأرقام */
.row-controls .control-btn {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    min-height: 18px !important;
    font-size: 8px !important;
}

/* توحيد تصميم جميع أزرار التحكم في الجداول */
#dutyTable .control-btn,
#patrolTable .control-btn,
#shiftsTable .control-btn {
    width: 24px !important;
    height: 24px !important;
    min-width: 24px !important;
    min-height: 24px !important;
    padding: 0 !important;
    margin: 1px !important;
    border-radius: 6px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* التأكد من أن جميع الأيقونات متسقة في جميع الجداول */
#dutyTable .control-btn i.fas,
#patrolTable .control-btn i.fas,
#shiftsTable .control-btn i.fas {
    font-size: 10px !important;
    line-height: 1 !important;
}

/* توحيد ألوان الأزرار في جميع الجداول */
#dutyTable .control-btn.btn-add,
#patrolTable .control-btn.btn-add,
#shiftsTable .control-btn.btn-add {
    color: #28a745 !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05)) !important;
}

#dutyTable .control-btn.btn-delete,
#patrolTable .control-btn.btn-delete,
#shiftsTable .control-btn.btn-delete {
    color: #dc3545 !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05)) !important;
}

/* إخفاء أي أزرار قديمة قد تكون موجودة */
.btn-sm.btn-success,
.btn-sm.btn-danger,
.btn-column-control,
.btn-add-column,
.row-delete-btn {
    display: none !important;
}

/* التأكد من أن الأزرار الموحدة تظهر */
.control-btn.btn-add,
.control-btn.btn-delete {
    display: inline-flex !important;
}

/* تحسين مظهر الأزرار عند التمرير */
#dutyTable .control-btn:hover,
#patrolTable .control-btn:hover,
#shiftsTable .control-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين مظهر الأزرار عند الضغط */
#dutyTable .control-btn:active,
#patrolTable .control-btn:active,
#shiftsTable .control-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
}

/* توحيد أزرار الصفوف في جميع الجداول */
#dutyTable .row-controls,
#patrolTable .row-controls,
#shiftsTable .row-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 1px !important;
    position: absolute !important;
    right: 2px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
    background: transparent !important;
    border-radius: 4px !important;
    padding: 1px !important;
}

/* إظهار أزرار الصفوف عند التمرير - بدون خلفية */
#dutyTable .row-number-cell:hover .row-controls,
#patrolTable .row-number-cell:hover .row-controls,
#shiftsTable .row-number-cell:hover .row-controls,
#dutyTable td:hover .row-controls,
#patrolTable td:hover .row-controls,
#shiftsTable td:hover .row-controls {
    opacity: 1 !important;
    transform: translateY(-50%) !important;
    background: transparent !important;
    backdrop-filter: none !important;
    box-shadow: none !important;
}

/* توحيد أزرار الصفوف في الوضع المظلم */
[data-theme="dark"] #receiptTable .row-controls,
[data-theme="dark"] #patrolTable .row-controls,
[data-theme="dark"] #shiftsTable .row-controls {
    background: transparent !important;
}

/* إظهار أزرار الصفوف عند التمرير في الوضع المظلم - بدون خلفية */
[data-theme="dark"] #receiptTable .row-number-cell:hover .row-controls,
[data-theme="dark"] #patrolTable .row-number-cell:hover .row-controls,
[data-theme="dark"] #shiftsTable .row-number-cell:hover .row-controls,
[data-theme="dark"] #receiptTable td:hover .row-controls,
[data-theme="dark"] #patrolTable td:hover .row-controls,
[data-theme="dark"] #shiftsTable td:hover .row-controls {
    background: transparent !important;
    backdrop-filter: none !important;
    box-shadow: none !important;
}

/* توحيد ألوان وتصميم جميع الخانات في الجداول */
#receiptTable .form-select,
#receiptTable .form-control,
#patrolTable .form-select,
#patrolTable .form-control,
#shiftsTable .form-select,
#shiftsTable .form-control {
    background-color: #000000 !important;
    border: 1px solid #dee2e6 !important;
    color: #ffffff !important;
    font-size: 14px !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    transition: all 0.15s ease-in-out !important;
}

/* حالة التركيز للخانات */
#receiptTable .form-select:focus,
#receiptTable .form-control:focus,
#patrolTable .form-select:focus,
#patrolTable .form-control:focus,
#shiftsTable .form-select:focus,
#shiftsTable .form-control:focus {
    background-color: #ffffff !important;
    border-color: #80bdff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    outline: 0 !important;
}

/* الخانات المعطلة */
#receiptTable .form-select:disabled,
#receiptTable .form-control:disabled,
#patrolTable .form-select:disabled,
#patrolTable .form-control:disabled,
#shiftsTable .form-select:disabled,
#shiftsTable .form-control:disabled {
    background-color: #e9ecef !important;
    opacity: 0.65 !important;
    cursor: not-allowed !important;
}

/* الوضع المظلم */
[data-theme="dark"] #receiptTable .form-select,
[data-theme="dark"] #receiptTable .form-control,
[data-theme="dark"] #patrolTable .form-select,
[data-theme="dark"] #patrolTable .form-control,
[data-theme="dark"] #shiftsTable .form-select,
[data-theme="dark"] #shiftsTable .form-control {
    background-color: #343a40 !important;
    border-color: #495057 !important;
    color: #ffffff !important;
}

[data-theme="dark"] #receiptTable .form-select:focus,
[data-theme="dark"] #receiptTable .form-control:focus,
[data-theme="dark"] #patrolTable .form-select:focus,
[data-theme="dark"] #patrolTable .form-control:focus,
[data-theme="dark"] #shiftsTable .form-select:focus,
[data-theme="dark"] #shiftsTable .form-control:focus {
    background-color: #495057 !important;
    border-color: #80bdff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* توحيد ألوان وتصميم جداول كشف الواجبات مع كشف الاستلامات */
#dutyTable,
#patrolTable,
#shiftsTable {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* رؤوس الجداول */
#dutyTable thead th,
#patrolTable thead th,
#shiftsTable thead th {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 12px 8px !important;
    border: 1px solid #495057 !important;
    font-size: 14px !important;
}

/* خلايا الجداول */
#dutyTable tbody td,
#patrolTable tbody td,
#shiftsTable tbody td {
    background-color: #000000 !important;
    border: 1px solid #dee2e6 !important;
    padding: 8px !important;
    text-align: center !important;
    vertical-align: middle !important;
    font-size: 13px !important;
    color: #ffffff !important;
}

/* الصفوف الزوجية */
#dutyTable tbody tr:nth-child(even) td,
#patrolTable tbody tr:nth-child(even) td,
#shiftsTable tbody tr:nth-child(even) td {
    background-color: #1a1a1a !important;
}

/* تأثير التمرير على الصفوف */
#dutyTable tbody tr:hover td,
#patrolTable tbody tr:hover td,
#shiftsTable tbody tr:hover td {
    background-color: #333333 !important;
    transition: background-color 0.15s ease-in-out !important;
}

/* الوضع المظلم للجداول */
[data-theme="dark"] #dutyTable,
[data-theme="dark"] #patrolTable,
[data-theme="dark"] #shiftsTable {
    background-color: #343a40 !important;
    border-color: #495057 !important;
}

[data-theme="dark"] #dutyTable thead th,
[data-theme="dark"] #patrolTable thead th,
[data-theme="dark"] #shiftsTable thead th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: #ffffff !important;
    border-color: #6c757d !important;
}

[data-theme="dark"] #dutyTable tbody td,
[data-theme="dark"] #patrolTable tbody td,
[data-theme="dark"] #shiftsTable tbody td {
    background-color: #495057 !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

[data-theme="dark"] #dutyTable tbody tr:nth-child(even) td,
[data-theme="dark"] #patrolTable tbody tr:nth-child(even) td,
[data-theme="dark"] #shiftsTable tbody tr:nth-child(even) td {
    background-color: #343a40 !important;
}

[data-theme="dark"] #dutyTable tbody tr:hover td,
[data-theme="dark"] #patrolTable tbody tr:hover td,
[data-theme="dark"] #shiftsTable tbody tr:hover td {
    background-color: #6c757d !important;
}





/* تحسين مظهر الحقول داخل الجداول */
#dutyTable .form-select,
#dutyTable .form-control,
#patrolTable .form-select,
#patrolTable .form-control,
#shiftsTable .form-select,
#shiftsTable .form-control {
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    font-size: 13px !important;
    padding: 6px 8px !important;
    transition: all 0.15s ease-in-out !important;
}

#dutyTable .form-select:focus,
#dutyTable .form-control:focus,
#patrolTable .form-select:focus,
#patrolTable .form-control:focus,
#shiftsTable .form-select:focus,
#shiftsTable .form-control:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* تنسيق العناوين القابلة للتحرير في جداول كشف الواجبات */
#dutyTable .editable-header,
#patrolTable .editable-header,
#shiftsTable .editable-header {
    background: transparent !important;
    border: none !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-align: center !important;
    width: 100% !important;
    padding: 4px !important;
    font-size: 14px !important;
}

#dutyTable .editable-header:focus,
#patrolTable .editable-header:focus,
#shiftsTable .editable-header:focus {
    background-color: rgba(255, 255, 255, 0.1) !important;
    outline: none !important;
    border-radius: 3px !important;
}

/* تنسيق أزرار التحكم في الأعمدة */
#dutyTable .column-controls,
#patrolTable .column-controls,
#shiftsTable .column-controls {
    display: flex !important;
    justify-content: center !important;
    gap: 4px !important;
    margin-top: 4px !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
}

/* إظهار أزرار الأعمدة عند التمرير */
#dutyTable th:hover .column-controls,
#patrolTable th:hover .column-controls,
#shiftsTable th:hover .column-controls {
    opacity: 1 !important;
}

#dutyTable .column-controls .control-btn,
#patrolTable .column-controls .control-btn,
#shiftsTable .column-controls .control-btn {
    width: 20px !important;
    height: 20px !important;
    font-size: 10px !important;
    padding: 0 !important;
    border-radius: 3px !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
}

/* تحسين خلايا الأرقام لتوفير مساحة للأزرار */
.row-number-cell {
    position: relative !important;
    overflow: visible !important;
    padding: 8px 25px 8px 8px !important;
    width: 60px !important;
    max-width: 60px !important;
}

/* تحسين عرض الأرقام */
.row-number {
    display: block !important;
    width: 100% !important;
    text-align: center !important;
    font-weight: bold !important;
    color: var(--text-primary) !important;
    font-size: 16px !important;
    line-height: 1.2 !important;
}

/* تحسين الأيقونات في أزرار الصفوف */
.row-controls .control-btn i.fas {
    font-size: 8px !important;
    line-height: 1 !important;
}


